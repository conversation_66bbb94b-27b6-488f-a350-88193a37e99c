import java.util.Scanner;
import java.util.Random;
public class CheckAlphabet {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        Random rand = new Random();
        String[] options = {"Bat", "Ball", "Stomp"};
        System.out.print("Enter your choice (<PERSON>, Ball, Stomp): ");
        String user = sc.next().toLowerCase();
        int compIndex = rand.nextInt(3);
        String comp = options[compIndex].toLowerCase();
        System.out.println("Computer chose: " + options[compIndex]);
        if(user.equals(comp))
            System.out.println("It's Tie!");
        else if((user.equals("bat") && comp.equals("ball")) ||
                (user.equals("ball") && comp.equals("stomp")) ||
                (user.equals("stomp") && comp.equals("bat")))
            System.out.println("User win!");
        else
            System.out.println("Computer win!");
    }
}
