import java.awt.*;
import javax.swing.*;
import java.awt.event.*;

public class ButtonExample1 extends J<PERSON>rame {
    public ButtonExample1() {
        // Create components
        JLabel label = new JLabel("Enter Your Name");
        JTextField textField = new JTextField("", 20);
        JButton button = new JButton("Click Me");

        // Add ActionListener to button
        button.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                String a = textField.getText();
                System.out.println("Button clicked! By " + a);
            }
        });

        // Set layout and add components
        setLayout(new FlowLayout());
        add(label);
        add(textField);
        add(button);

        // J<PERSON>rame settings
        setTitle("Button Example");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(400, 400);
        setVisible(true);
    }

    public static void main(String[] args) {
        // Run GUI in Event Dispatch Thread
        SwingUtilities.invokeLater(new Runnable() {
            public void run() {
                new ButtonExample1();
            }
        });
    }
}
