class Box<T>{
T data;
public Box(T data){
this.data=data;
}
public T getData(){
return data;}}
class BoxE{
public static void main (String args[]){
Box<Integer> intBox= new Box<Integer>(42);
int x=intBox.getData();
System.out.println("x="+""+x);
Box<Double> doubleBox= new Box<Double>(42.0);
double y=doubleBox.getData();
System.out.println("y="+""+y);
Box<String> strBox= new Box<String>("Name= Hariom");
String s=strBox.getData();
System.out.println("s="+""+s);
}}