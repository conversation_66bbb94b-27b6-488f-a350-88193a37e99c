import java.lang.*;
class box{
private double width;
private double height;
private double depth;
box(box ob){
width=ob.width;
height=ob.height;
depth=ob.depth;
}
box(double w,double h,double d){
width=w;
height=h;
depth=d;
}
box(){
width=-1;
height=-1;
depth=-1;
}
box(double len){
width=height=depth=len;
}
double volume(){
return width*height*depth;
}}
class boxweight extends box{
double weight;
boxweight(boxweight ob){
super(ob);
weight=ob.weight;
}
boxweight(double w, double h,double d, double m){
super(w,h,d);
weight=m;
}
boxweight(){
super();
weight=-1;
}
boxweight(double len, double m){
super(len);
weight=m;
}}

class Demosuper{
public static void main(String args[]){
boxweight mybox1=new boxweight(10,20,15,34.3);
boxweight mybox2=new boxweight(2,3,4,0.076);
boxweight mybox3=new boxweight();
boxweight mycube=new boxweight(mybox1);
double vol;
vol=mybox1.voulme();
System.out.println("volume of my box1 is"+vol);
System.out.println("weight of my box1 is"+mybox1.weight);
System.out.println();
vol=mybox2voulme();
System.out.println("volume of my box2is"+vol);
System.out.println("weight of my box2is"+mybox2.weight);
System.out.println();
}}



