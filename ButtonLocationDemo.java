import javax.swing.*;  import java.awt.*;
import java.awt.event.ActionListener; import java.awt.event.KeyEvent;
import java.awt.event.ActionEvent;    import java.awt.event.KeyAdapter;

public class ButtonLocationDemo extends J<PERSON>rame{
private JButton button;
private JLabel  label;
public ButtonLocationDemo(){
      JPanel p = new JPanel();
      label = new JLabel("Enter Your Name :"); p.setLayout(null); label.setBounds(35,85,150,80); p.add(label);
      
      JTextField textField = new JTextField(" ", 15); p.setLayout(null); textField.setBounds(185,115,120,25); p.add(textField);
     
      button = new JButton("Save"); p.setLayout(null); button.setBounds(100,150,100,20); p.add(button);
      
      button.addActionListener(new ActionListener() {public void actionPerformed(ActionEvent e){String a=textField.getText();
         	      System.out.println("Button clicked! By " + a);  textField.setText("");}  });
      
      button.addKeyListener(new KeyAdapter() { public void keyPressed(KeyEvent e) { String a=textField.getText();
                System.out.println("Key Pressed : " + a); 
            }  });
	
      getContentPane().add(p);   setDefaultCloseOperation(3);  setSize(600,600); setVisible(true); 
     }
  
    public static void main(String args[]){ 
           new ButtonLocationDemo();}           }