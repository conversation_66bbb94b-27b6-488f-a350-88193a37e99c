import java.util.*;
class MathBox<E extends Number>extends Box<Number>
{
public MathBox(E data)
{
super(data);
}
public double sqrt()
{
return Math.sqrt(getData().doubleValue());
}
public static void main(String args[]){
MathBox<Integer> ss=new MathBox<Integer>(22);
MathBox<Double> sss=new MathBox<Double>(22.0);
MathBox<String> ssss=new MathBox<String>(22.0);
System.out.println(ss.sqrt());
System.out.println(sss.sqrt());
}}