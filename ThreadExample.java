class ThreadExample extends Thread {
   public void run () {
			try{
			    int c=2;
      for (int i = 1; i <= 10; i++) {
        System.out.println(c+" " + "* " + i +"= " + c*i);
	System.out.println("Thread: " + i);
	System.out.println("Thread Name " + Thread.currentThread().getName());
	Thread.sleep(5000);                      }
	} catch(Exception e){System.out.println(e); }
	
   }
}


class RunnableExample implements Runnable {
	 
	public void run () {
		try {
		int d=3;
		wait();
		//Thread.sleep(5000); 
		for (int i = 1; i <= 10; i++) {
		System.out.println(d+" " + "* " + i +"= " + d*i);
         	System.out.println ("Runnable: " + i);
		System.out.println("Thread Name " + Thread.currentThread().getName());
		    	}
	} catch(Exception e){System.out.println(e);}
	
   	}
}

public class MultithreadingDemo {
       public static void main (String argv[]) {
              new ThreadExample ().start ();
	      new Thread(new RunnableExample ()).start ();
		       }
}