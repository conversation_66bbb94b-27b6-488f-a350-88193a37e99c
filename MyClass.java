class MyClass {
    public void myMethod() {
        System.out.println("Executing myMethod on instance: " + this);
    }
}

public class Main {
    public static void main(String[] args) {
        MyClass obj1 = new MyClass();
        MyClass obj2 = new MyClass();

        // Bounded method reference
        Runnable task1 = obj1::myMethod;
        Runnable task2 = obj2::myMethod;

        // Executing the tasks
        task1.run(); // Output: Executing myMethod on instance: MyClass@... (obj1's hashcode)
        task2.run(); // Output: Executing myMethod on instance: MyClass@... (obj2's hashcode)
    }
}